package com.knet.goods.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.knet.common.annotation.Loggable;
import com.knet.common.annotation.ModifyHeader;
import com.knet.common.base.HttpResult;
import com.knet.common.constants.SystemConstant;
import com.knet.goods.model.dto.req.ProductDetailsQueryRequest;
import com.knet.goods.model.dto.req.ProductQueryRequest;
import com.knet.goods.model.dto.resp.ProductBySkuDtoResp;
import com.knet.goods.model.dto.resp.ProductSkuSpecPriceDtoResp;
import com.knet.goods.service.IKnetProductService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/19 16:43
 * @description: 商品控制器
 */
@Slf4j
@RestController
@RequestMapping("/product")
@Tag(name = "商品控制器", description = "商品控制器")
public class ProductController {
    @Resource
    private IKnetProductService iKnetProductService;

    /**
     * 商品详情
     *
     * @param request 查询条件
     * @return 商品详情
     * @see com.knet.goods.model.dto.req.ProductDetailsQueryRequest
     */
    @Loggable(value = "查询-商品详情")
    @ModifyHeader(value = SystemConstant.TOKEN, handlerType = "USER_TOKEN")
    @Operation(description = "商品详情")
    @PostMapping("/detail")
    public HttpResult<List<ProductSkuSpecPriceDtoResp>> queryProductDetails(@Validated @RequestBody ProductDetailsQueryRequest request) {
        return HttpResult.ok(iKnetProductService.queryProductDetails(request));
    }

    /**
     * 商品列表
     *
     * @param request 查询条件
     * @return 商品列表
     * @see com.knet.goods.model.dto.req.ProductQueryRequest
     */
    @Loggable(value = "查询-商品列表")
    @ModifyHeader(value = SystemConstant.TOKEN, handlerType = "USER_TOKEN")
    @Operation(description = "商品列表")
    @PostMapping("/list")
    public HttpResult<IPage<ProductBySkuDtoResp>> queryProductGroupBySku(@RequestBody ProductQueryRequest request) {
        return HttpResult.ok(iKnetProductService.queryProductGroupBySku(request));
    }

    /**
     * 查询商品品牌（废弃）
     *
     * @return 商品品牌列表
     */
    @Deprecated
    @Operation(description = "商品品牌")
    @GetMapping("/brands")
    public HttpResult<List<String>> queryProductBrands() {
        return HttpResult.ok(iKnetProductService.queryProductBrands());
    }
}
