package com.knet.payment.mq.producer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSON;
import com.knet.common.dto.message.UserOperationRecordMessage;
import com.knet.common.utils.RandomStrUtil;
import com.knet.payment.system.event.PaymentFailedEvent;
import com.knet.payment.system.event.PaymentResultEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2025/5/20 20:10
 * @description: 用户操作记录生产者
 */
@Slf4j
@Component
public class PaymentMessageProducer {
    @Resource
    private RandomStrUtil randomStrUtil;
    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 发送用户操作记录消息
     *
     * @param recordMessage 操作记录消息
     */
    public void sendUserOperationRecord(UserOperationRecordMessage recordMessage) {
        String messageId = randomStrUtil.getMessageId();
        String messageBody = JSON.toJSONString(recordMessage);
        log.info("发送用户操作记录消息: messageId={}, userId={}, operationType={}", messageId, recordMessage.getUserId(), recordMessage.getOperationType());
        MessageProperties properties = new MessageProperties();
        properties.setHeader("routingKey", "user.operation.record");
        properties.setHeader("messageId", messageId);
        properties.setDeliveryMode(MessageDeliveryMode.PERSISTENT);
        Message message = new Message(messageBody.getBytes(StandardCharsets.UTF_8), properties);
        // 发送消息（使用确认回调）
        CorrelationData correlationData = new CorrelationData(messageId);
        rabbitTemplate.convertAndSend(
                "user-operation-exchange",
                "user.operation.record",
                message,
                correlationData
        );

        // 设置回调
        correlationData.getFuture().addCallback(
                result -> {
                    assert result != null;
                    if (BeanUtil.isNotEmpty(result) && result.isAck()) {
                        log.info("用户操作记录消息到达Broker: {}", messageId);
                    } else {
                        log.error("用户操作记录消息未到达Broker: {}", messageId);
                    }
                },
                ex -> {
                    // 消息补偿机制，记录消息发送失败的消息，重试发送
                    log.error("用户操作记录消息发送异常: messageId={}, error={}", messageId, ex.getMessage());
                    // TODO: 可以在这里实现消息重试机制或者记录到数据库
                }
        );
    }

    /**
     * 发送支付结果事件
     */
    public void sendPaymentResultEvent(PaymentResultEvent resultEvent) {
        String messageId = String.format("PAY_RESULT_%s", RandomUtil.randomString(16));
        String messageBody = JSON.toJSONString(resultEvent);
        log.info("发送支付结果事件: messageId={}, paymentId={}, userId={}, orderId={}, status={}"
                , messageId, resultEvent.getPaymentId(), resultEvent.getUserId(), resultEvent.getOrderId(), resultEvent.getStatus());
        MessageProperties properties = new MessageProperties();
        properties.setHeader("routingKey", "payment.result");
        properties.setHeader("messageId", messageId);
        properties.setDeliveryMode(MessageDeliveryMode.PERSISTENT);
        Message message = new Message(messageBody.getBytes(StandardCharsets.UTF_8), properties);
        // 发送消息（使用确认回调）
        CorrelationData correlationData = new CorrelationData(messageId);
        rabbitTemplate.convertAndSend(
                "payment-result-exchange",
                "payment.result",
                message,
                correlationData
        );
        // 设置回调
        correlationData.getFuture().addCallback(
                result -> {
                    assert result != null;
                    if (BeanUtil.isNotEmpty(result) && result.isAck()) {
                        log.info("支付结果事件到达Broker: {}", messageId);
                    } else {
                        log.error("支付结果事件未到达Broker: {}", messageId);
                    }
                },
                ex -> {
                    log.error("支付结果事件发送异常: messageId={}, error={}", messageId, ex.getMessage());
                }
        );
    }

    /**
     * 发送支付失败事件
     *
     * @param paymentId      支付流水ID
     * @param userId         用户ID
     * @param orderId        订单ID
     * @param amount         支付金额
     * @param paymentChannel 支付渠道
     * @param errorMessage   错误信息
     */
    public void sendPaymentFailedEvent(String paymentId, Long userId, String orderId,
                                       String amount, String paymentChannel, String errorMessage) {
        String messageId = String.format("PAY_FAILED_%s", RandomUtil.randomString(16));
        // 构建支付失败事件消息体
        PaymentFailedEvent failedEvent = PaymentFailedEvent
                .builder()
                .paymentId(paymentId)
                .userId(userId)
                .orderId(orderId)
                .amount(amount)
                .paymentChannel(paymentChannel)
                .errorMessage(errorMessage)
                .failedTime(System.currentTimeMillis())
                .build();
        String messageBody = JSON.toJSONString(failedEvent);
        log.info("发送支付失败事件: messageId={}, paymentId={}, userId={}, orderId={}",
                messageId, paymentId, userId, orderId);
        MessageProperties properties = new MessageProperties();
        properties.setHeader("routingKey", "payment.failed");
        properties.setHeader("messageId", messageId);
        properties.setDeliveryMode(MessageDeliveryMode.PERSISTENT);
        Message message = new Message(messageBody.getBytes(StandardCharsets.UTF_8), properties);
        // 发送消息（使用确认回调）
        CorrelationData correlationData = new CorrelationData(messageId);
        rabbitTemplate.convertAndSend(
                "payment-result-exchange",
                "payment.failed",
                message,
                correlationData
        );
        // 设置回调
        correlationData.getFuture().addCallback(
                result -> {
                    assert result != null;
                    if (BeanUtil.isNotEmpty(result) && result.isAck()) {
                        log.info("支付失败事件到达Broker: {}", messageId);
                    } else {
                        log.error("支付失败事件未到达Broker: {}", messageId);
                    }
                },
                ex -> {
                    log.error("支付失败事件发送异常: messageId={}, error={}", messageId, ex.getMessage());
                }
        );
    }

    /**
     * 支付失败消息体（保留原有的，用于兼容）
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class PaymentFailMessage {
        private String paymentId;
        private Long userId;
        private String orderId;
        private String amount;
        private String paymentChannel;
        private String errorMessage;
        private Long failTime;
    }
}
