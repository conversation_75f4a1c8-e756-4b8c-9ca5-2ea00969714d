-- 子订单表建表语句
CREATE TABLE `sys_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` varchar(64) NOT NULL COMMENT '子订单ID（全局唯一）',
  `parent_order_id` varchar(64) NOT NULL COMMENT '母订单ID，关联母订单',
  `user_id` bigint NOT NULL COMMENT '用户ID，关联企业账户',
  `sku` varchar(100) NOT NULL COMMENT '商品SKU',
  `product_name` varchar(255) NOT NULL COMMENT '商品名称',
  `image_url` varchar(500) DEFAULT NULL COMMENT '商品图片URL',
  `total_amount` decimal(10,2) NOT NULL COMMENT '子订单总金额（含税价，单位：美元）',
  `total_quantity` int NOT NULL COMMENT '商品总数量',
  `status` varchar(50) NOT NULL COMMENT '状态：PENDING_PAYMENT-待支付,PROCESSING-支付中,PAID-已支付,PARTIAL_SUCCESS-部分成功,CANCELLED-已取消',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除，0 表示未删除， 1 表示已删除',
  `version` int NOT NULL DEFAULT '1' COMMENT '版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_id` (`order_id`),
  KEY `idx_parent_order_id` (`parent_order_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_sku` (`sku`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='B2B子订单表';

-- 修改订单明细表注释，明确关联到子订单
ALTER TABLE `sys_order_item` MODIFY COLUMN `order_id` varchar(64) NOT NULL COMMENT '外键关联子订单ID';
ALTER TABLE `sys_order_item` COMMENT = 'B2B订单明细表（关联子订单）';

-- 为订单明细表添加索引优化
ALTER TABLE `sys_order_item` ADD INDEX `idx_order_id_sku_size` (`order_id`, `sku`, `size`);
ALTER TABLE `sys_order_item` ADD INDEX `idx_sku_size_price` (`sku`, `size`, `price`);
