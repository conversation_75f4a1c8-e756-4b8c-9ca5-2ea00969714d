package com.knet.order.controller;

import com.knet.common.annotation.Loggable;
import com.knet.common.base.HttpResult;
import com.knet.order.model.dto.req.CreateOrderRequest;
import com.knet.order.model.dto.rsp.CreateOrderResponse;
import com.knet.order.service.ISysOrderGroupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/6/3 15:58
 * @description: 订单控制器
 */
@Slf4j
@RestController
@RequestMapping("/order")
@Tag(name = "订单控制器", description = "订单控制器")
public class OrderController {

    @Resource
    private ISysOrderGroupService orderGroupService;

    /**
     * 从购物车创建订单
     *
     * @param request 创建订单请求
     * @return 创建订单响应
     */
    @Loggable(value = "从购物车创建订单")
    @Operation(summary = "从购物车创建订单", description = "用户从购物车触发创建订单，订单创建完毕本地事务提交后，发送MQ消息到order.created")
    @PostMapping("/create")
    public HttpResult<CreateOrderResponse> createOrder(@Validated @RequestBody CreateOrderRequest request) {
        log.info("创建订单请求: {}", request);
        CreateOrderResponse response = orderGroupService.createOrderFromCart(request);
        return HttpResult.ok(response);
    }
}
