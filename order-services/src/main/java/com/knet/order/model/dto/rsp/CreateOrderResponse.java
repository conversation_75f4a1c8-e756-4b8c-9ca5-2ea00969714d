package com.knet.order.model.dto.rsp;

import com.knet.common.enums.KnetOrderStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/3 16:35
 * @description: 创建订单响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "创建订单响应")
public class CreateOrderResponse {

    @Schema(description = "订单ID")
    private String orderId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "订单总金额（美元）")
    private String totalAmount;

    @Schema(description = "订单状态")
    private KnetOrderStatus status;

    @Schema(description = "订单商品项列表")
    private List<OrderItemResponse> items;

    /**
     * 订单商品项响应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "订单商品项响应")
    public static class OrderItemResponse {

        @Schema(description = "订单项ID")
        private Long itemId;

        @Schema(description = "商品SKU")
        private String sku;

        @Schema(description = "商品名称")
        private String name;

        @Schema(description = "尺码")
        private String size;

        @Schema(description = "单价（美元）")
        private String price;

        @Schema(description = "数量")
        private Integer count;

        @Schema(description = "小计金额（美元）")
        private String subtotal;
    }
}
