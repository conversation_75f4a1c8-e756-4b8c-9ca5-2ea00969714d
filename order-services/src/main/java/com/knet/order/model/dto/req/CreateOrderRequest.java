package com.knet.order.model.dto.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/3 16:30
 * @description: 创建订单请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "创建订单请求")
public class CreateOrderRequest {

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "订单商品项列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "订单商品项不能为空")
    @Valid
    private List<OrderItemRequest> items;

    /**
     * 订单商品项请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "订单商品项请求")
    public static class OrderItemRequest {

        @Schema(description = "商品SKU", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "商品SKU不能为空")
        private String sku;

        @Schema(description = "尺码明细列表", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotEmpty(message = "尺码明细不能为空")
        @Valid
        private List<SizeDetailRequest> sizeDetails;
    }

    /**
     * 尺码明细请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "尺码明细请求")
    public static class SizeDetailRequest {

        @Schema(description = "尺码值", requiredMode = Schema.RequiredMode.REQUIRED, example = "5")
        @NotNull(message = "尺码值不能为空")
        private String size;

        @Schema(description = "数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        @NotNull(message = "数量不能为空")
        private Integer quantity;
    }
}
