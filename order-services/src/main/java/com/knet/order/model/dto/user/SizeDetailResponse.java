package com.knet.order.model.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/6/3 17:00
 * @description: 尺码明细响应DTO（订单服务使用）
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "尺码明细响应")
public class SizeDetailResponse {

    @Schema(description = "尺码明细ID")
    private Long detailId;

    @Schema(description = "购物车商品项ID")
    private Long cartItemId;

    @Schema(description = "尺码值", example = "5")
    private String size;

    @Schema(description = "数量", example = "1")
    private Integer quantity;

    @Schema(description = "单价，美分", example = "9999")
    private String unitPrice;

    @Schema(description = "小计金额，美元", example = "9999")
    private String subtotal;

    @Schema(description = "是否选中 (0-未选中, 1-已选中)", example = "1")
    private Integer selected;
}
