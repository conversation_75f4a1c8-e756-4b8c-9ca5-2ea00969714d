package com.knet.order.service.impl;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.enums.KnetOrderItemStatus;
import com.knet.common.enums.KnetOrderStatus;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.PriceFormatUtil;
import com.knet.common.utils.RandomStrUtil;
import com.knet.order.mapper.SysOrderGroupMapper;
import com.knet.order.model.dto.req.CreateOrderRequest;
import com.knet.order.model.dto.rsp.CreateOrderResponse;
import com.knet.order.model.dto.user.CartItemResponse;
import com.knet.order.model.dto.user.CartResponse;
import com.knet.order.model.dto.user.SizeDetailSumResponse;
import com.knet.order.model.entity.SysOrder;
import com.knet.order.model.entity.SysOrderGroup;
import com.knet.order.model.entity.SysOrderItem;
import com.knet.order.openfeign.ApiUserServiceProvider;
import com.knet.order.service.ISysOrderGroupService;
import com.knet.order.service.ISysOrderItemService;
import com.knet.order.service.ISysOrderService;
import com.knet.order.system.event.OrderCreatedEvent;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-03-11 15:45:02
 * @description: 针对表【sys_order(B2B订单主表)】的数据库操作Service实现
 */
@Slf4j
@Service
public class SysOrderGroupServiceImpl extends ServiceImpl<SysOrderGroupMapper, SysOrderGroup> implements ISysOrderGroupService {
    @Resource
    private ApplicationEventPublisher eventPublisher;
    @Resource
    private ISysOrderService orderService;
    @Resource
    private ISysOrderItemService orderItemService;
    @Resource
    private ApiUserServiceProvider userServiceProvider;
    @Resource
    private RandomStrUtil randomStrUtil;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateOrderResponse createOrderFromCart(CreateOrderRequest request) {
        log.info("开始创建子母订单, userId: {}, items: {}", request.getUserId(), request.getItems().size());

        // 1. 验证请求数据并按商品分组
        Map<String, List<OrderItemData>> productGroupMap = validateAndGroupOrderItemsFromRequest(request);
        if (productGroupMap.isEmpty()) {
            throw new ServiceException("订单商品数据为空");
        }

        // 2. 生成母订单ID
        String parentOrderId = randomStrUtil.getOrderId();

        // 3. 计算母订单总金额
        BigDecimal totalAmount = calculateTotalAmountFromGroups(productGroupMap);

        // 4. 创建母订单记录
        SysOrderGroup orderGroup = createOrderGroup(parentOrderId, request.getUserId(), totalAmount);
        this.save(orderGroup);

        // 5. 为每个商品创建子订单和明细
        List<SubOrderData> subOrderDataList = createSubOrdersAndItems(parentOrderId, request.getUserId(), productGroupMap);

        // 6. 发送订单创建事件
        OrderCreatedEvent event = new OrderCreatedEvent(this, orderGroup);
        pushOrderCreateEvent(event);

        // 7. 构建响应
        CreateOrderResponse response = buildCreateOrderResponse(orderGroup, subOrderDataList);
        log.info("子母订单创建成功, parentOrderId: {}, subOrders: {}, totalAmount: {}",
                parentOrderId, subOrderDataList.size(), totalAmount);
        return response;
    }

    /**
     * 发送订单创建事件
     *
     * @param orderCreatedEvent 订单创建事件
     */
    public void pushOrderCreateEvent(OrderCreatedEvent orderCreatedEvent) {
        eventPublisher.publishEvent(orderCreatedEvent);
    }

    /**
     * 获取用户购物车信息
     */
    private CartResponse getUserCart(Long userId) {
        try {
            var result = userServiceProvider.getCart(userId);
            if (result != null && result.isSuccess()) {
                return result.getData();
            }
            log.error("获取用户购物车失败, userId: {}, result: {}", userId, result);
            throw new ServiceException("获取用户购物车信息失败");
        } catch (Exception e) {
            log.error("调用用户服务获取购物车异常, userId: {}", userId, e);
            throw new ServiceException("获取用户购物车信息失败: " + e.getMessage());
        }
    }

    /**
     * 验证请求数据并按商品分组（支持相同SKU相同尺码不同定价）
     */
    private Map<String, List<OrderItemData>> validateAndGroupOrderItemsFromRequest(CreateOrderRequest request) {
        Map<String, List<OrderItemData>> productGroupMap = new HashMap<>();

        for (CreateOrderRequest.OrderItemRequest requestItem : request.getItems()) {
            List<OrderItemData> itemDataList = new ArrayList<>();

            // 处理每个尺码明细（支持相同尺码不同定价）
            for (CreateOrderRequest.SizeDetailRequest sizeDetailRequest : requestItem.getSizeDetails()) {
                // 验证基础数据
                if (sizeDetailRequest.getQuantity() <= 0) {
                    throw new ServiceException("商品 " + requestItem.getSku() + " 尺码 " + sizeDetailRequest.getSize() + " 数量必须大于0");
                }

                // 验证单价格式
                BigDecimal unitPrice;
                try {
                    unitPrice = new BigDecimal(sizeDetailRequest.getUnitPrice());
                    if (unitPrice.compareTo(BigDecimal.ZERO) <= 0) {
                        throw new ServiceException("商品 " + requestItem.getSku() + " 尺码 " + sizeDetailRequest.getSize() + " 单价必须大于0");
                    }
                } catch (NumberFormatException e) {
                    throw new ServiceException("商品 " + requestItem.getSku() + " 尺码 " + sizeDetailRequest.getSize() + " 单价格式错误");
                }

                // 创建订单商品数据（每个尺码明细对应一条记录，支持相同尺码不同定价）
                OrderItemData orderItemData = new OrderItemData();
                orderItemData.setSku(requestItem.getSku());
                orderItemData.setProductName(requestItem.getProductName());
                orderItemData.setImageUrl(requestItem.getImageUrl());
                orderItemData.setSize(sizeDetailRequest.getSize());
                orderItemData.setQuantity(sizeDetailRequest.getQuantity());
                orderItemData.setUnitPrice(unitPrice);
                itemDataList.add(orderItemData);
            }

            // 合并同一SKU的商品数据
            if (productGroupMap.containsKey(requestItem.getSku())) {
                productGroupMap.get(requestItem.getSku()).addAll(itemDataList);
            } else {
                productGroupMap.put(requestItem.getSku(), itemDataList);
            }
        }

        return productGroupMap;
    }

    /**
     * 计算母订单总金额（从商品分组计算）
     */
    private BigDecimal calculateTotalAmountFromGroups(Map<String, List<OrderItemData>> productGroupMap) {
        return productGroupMap.values().stream()
                .flatMap(List::stream)
                .map(item -> item.getUnitPrice().multiply(new BigDecimal(item.getQuantity())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算单个商品的总金额
     */
    private BigDecimal calculateProductTotalAmount(List<OrderItemData> itemDataList) {
        return itemDataList.stream()
                .map(item -> item.getUnitPrice().multiply(new BigDecimal(item.getQuantity())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算单个商品的总数量
     */
    private Integer calculateProductTotalQuantity(List<OrderItemData> itemDataList) {
        return itemDataList.stream()
                .mapToInt(OrderItemData::getQuantity)
                .sum();
    }

    /**
     * 创建订单主表记录
     */
    private SysOrderGroup createOrderGroup(String orderId, Long userId, BigDecimal totalAmount) {
        SysOrderGroup orderGroup = new SysOrderGroup();
        orderGroup.setOrderId(orderId);
        orderGroup.setUserId(userId);
        orderGroup.setTotalAmount(totalAmount);
        orderGroup.setStatus(KnetOrderStatus.PENDING_PAYMENT);
        return orderGroup;
    }

    /**
     * 为每个商品创建子订单和明细
     */
    private List<SubOrderData> createSubOrdersAndItems(String parentOrderId, Long userId,
                                                       Map<String, List<OrderItemData>> productGroupMap) {
        List<SubOrderData> subOrderDataList = new ArrayList<>();

        for (Map.Entry<String, List<OrderItemData>> entry : productGroupMap.entrySet()) {
            String sku = entry.getKey();
            List<OrderItemData> itemDataList = entry.getValue();

            // 生成子订单ID
            String subOrderId = randomStrUtil.getOrderId();

            // 计算子订单金额和数量
            BigDecimal subOrderAmount = calculateProductTotalAmount(itemDataList);
            Integer subOrderQuantity = calculateProductTotalQuantity(itemDataList);

            // 创建子订单
            SysOrder subOrder = new SysOrder();
            subOrder.setOrderId(subOrderId);
            subOrder.setParentOrderId(parentOrderId);
            subOrder.setUserId(userId);
            subOrder.setSku(sku);
            subOrder.setProductName(itemDataList.get(0).getProductName());
            subOrder.setImageUrl(itemDataList.get(0).getImageUrl());
            subOrder.setTotalAmount(subOrderAmount);
            subOrder.setTotalQuantity(subOrderQuantity);
            subOrder.setStatus(KnetOrderStatus.PENDING_PAYMENT);

            // 保存子订单
            orderService.save(subOrder);

            // 创建订单明细
            List<SysOrderItem> orderItems = createOrderItems(subOrderId, itemDataList);
            orderItemService.saveBatch(orderItems);

            // 构建子订单数据
            SubOrderData subOrderData = new SubOrderData();
            subOrderData.setSubOrder(subOrder);
            subOrderData.setOrderItems(orderItems);
            subOrderDataList.add(subOrderData);
        }

        return subOrderDataList;
    }

    /**
     * 创建订单明细记录
     */
    private List<SysOrderItem> createOrderItems(String subOrderId, List<OrderItemData> orderItemDataList) {
        List<SysOrderItem> orderItems = new ArrayList<>();

        for (OrderItemData itemData : orderItemDataList) {
            SysOrderItem orderItem = new SysOrderItem();
            orderItem.setOrderId(subOrderId);
            orderItem.setSku(itemData.getSku());
            orderItem.setSize(itemData.getSize());
            orderItem.setName(itemData.getProductName());
            orderItem.setPrice(itemData.getUnitPrice());
            orderItem.setCount(itemData.getQuantity());
            orderItem.setStatus(KnetOrderItemStatus.PENDING_PAYMENT);
            orderItems.add(orderItem);
        }

        return orderItems;
    }

    /**
     * 构建创建订单响应
     */
    private CreateOrderResponse buildCreateOrderResponse(SysOrderGroup orderGroup, List<SubOrderData> subOrderDataList) {
        List<CreateOrderResponse.SubOrderResponse> subOrderResponses = subOrderDataList.stream()
                .map(subOrderData -> {
                    SysOrder subOrder = subOrderData.getSubOrder();
                    List<SysOrderItem> orderItems = subOrderData.getOrderItems();

                    List<CreateOrderResponse.OrderItemResponse> itemResponses = orderItems.stream()
                            .map(item -> CreateOrderResponse.OrderItemResponse.builder()
                                    .itemId(item.getItemId())
                                    .sku(item.getSku())
                                    .name(item.getName())
                                    .size(item.getSize())
                                    .price(PriceFormatUtil.formatPrice(item.getPrice()))
                                    .count(item.getCount())
                                    .subtotal(PriceFormatUtil.formatPrice(item.getPrice().multiply(new BigDecimal(item.getCount()))))
                                    .build())
                            .collect(Collectors.toList());

                    return CreateOrderResponse.SubOrderResponse.builder()
                            .orderId(subOrder.getOrderId())
                            .sku(subOrder.getSku())
                            .productName(subOrder.getProductName())
                            .imageUrl(subOrder.getImageUrl())
                            .totalAmount(PriceFormatUtil.formatPrice(subOrder.getTotalAmount()))
                            .totalQuantity(subOrder.getTotalQuantity())
                            .status(subOrder.getStatus())
                            .items(itemResponses)
                            .build();
                })
                .collect(Collectors.toList());

        return CreateOrderResponse.builder()
                .parentOrderId(orderGroup.getOrderId())
                .userId(orderGroup.getUserId())
                .totalAmount(PriceFormatUtil.formatPrice(orderGroup.getTotalAmount()))
                .status(orderGroup.getStatus())
                .subOrders(subOrderResponses)
                .build();
    }

    /**
     * 订单商品数据内部类
     */
    @Data
    private static class OrderItemData {
        private String sku;
        private String productName;
        private String imageUrl;
        private String size;
        private Integer quantity;
        private BigDecimal unitPrice;
    }

    /**
     * 子订单数据内部类
     */
    @Data
    private static class SubOrderData {
        private SysOrder subOrder;
        private List<SysOrderItem> orderItems;
    }
}
