package com.knet.order.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.enums.KnetOrderItemStatus;
import com.knet.common.enums.KnetOrderStatus;
import com.knet.common.exception.ServiceException;
import com.knet.common.utils.PriceFormatUtil;
import com.knet.common.utils.RandomStrUtil;
import com.knet.order.mapper.SysOrderGroupMapper;
import com.knet.order.model.dto.req.CreateOrderRequest;
import com.knet.order.model.dto.rsp.CreateOrderResponse;
import com.knet.order.model.entity.SysOrderGroup;
import com.knet.order.model.entity.SysOrderItem;
import com.knet.order.openfeign.ApiUserServiceProvider;
import com.knet.order.service.ISysOrderGroupService;
import com.knet.order.service.ISysOrderItemService;
import com.knet.order.system.event.OrderCreatedEvent;
import com.knet.user.model.dto.rsp.CartItemResponse;
import com.knet.user.model.dto.rsp.CartResponse;
import com.knet.user.model.dto.rsp.SizeDetailSumResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-03-11 15:45:02
 * @description: 针对表【sys_order(B2B订单主表)】的数据库操作Service实现
 */
@Slf4j
@Service
public class SysOrderGroupServiceImpl extends ServiceImpl<SysOrderGroupMapper, SysOrderGroup> implements ISysOrderGroupService {
    @Resource
    private ApplicationEventPublisher eventPublisher;

    @Resource
    private ISysOrderItemService orderItemService;

    @Resource
    private ApiUserServiceProvider userServiceProvider;

    @Resource
    private RandomStrUtil randomStrUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrder(SysOrderGroup sysOrderGroup) {
        // 保存订单信息
        this.save(sysOrderGroup);
        // 注册事务同步回调
        OrderCreatedEvent event = new OrderCreatedEvent(this, sysOrderGroup);
        pushOrderCreateEvent(event);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateOrderResponse createOrderFromCart(CreateOrderRequest request) {
        log.info("开始从购物车创建订单, userId: {}, items: {}", request.getUserId(), request.getItems().size());

        // 1. 获取用户购物车信息
        CartResponse cartResponse = getUserCart(request.getUserId());
        if (cartResponse == null || CollUtil.isEmpty(cartResponse.getItems())) {
            throw new ServiceException("购物车为空，无法创建订单");
        }

        // 2. 验证请求的商品是否在购物车中，并获取匹配的商品信息
        List<OrderItemData> orderItemDataList = validateAndGetOrderItems(request, cartResponse);
        if (CollUtil.isEmpty(orderItemDataList)) {
            throw new ServiceException("请求的商品在购物车中不存在或数量不足");
        }

        // 3. 生成订单ID
        String orderId = randomStrUtil.getOrderId();

        // 4. 计算订单总金额
        BigDecimal totalAmount = calculateTotalAmount(orderItemDataList);

        // 5. 创建订单主表记录
        SysOrderGroup orderGroup = createOrderGroup(orderId, request.getUserId(), totalAmount);
        this.save(orderGroup);

        // 6. 创建订单明细记录
        List<SysOrderItem> orderItems = createOrderItems(orderId, orderItemDataList);
        orderItemService.saveBatch(orderItems);

        // 7. 发送订单创建事件
        OrderCreatedEvent event = new OrderCreatedEvent(this, orderGroup);
        pushOrderCreateEvent(event);

        // 8. 构建响应
        CreateOrderResponse response = buildCreateOrderResponse(orderGroup, orderItems);
        log.info("订单创建成功, orderId: {}, totalAmount: {}", orderId, totalAmount);

        return response;
    }

    /**
     * 发送订单创建事件
     *
     * @param orderCreatedEvent 订单创建事件
     */
    public void pushOrderCreateEvent(OrderCreatedEvent orderCreatedEvent) {
        eventPublisher.publishEvent(orderCreatedEvent);
    }

    /**
     * 获取用户购物车信息
     */
    private CartResponse getUserCart(Long userId) {
        try {
            var result = userServiceProvider.getCart(userId);
            if (result != null && result.isSuccess()) {
                return result.getData();
            }
            log.error("获取用户购物车失败, userId: {}, result: {}", userId, result);
            throw new ServiceException("获取用户购物车信息失败");
        } catch (Exception e) {
            log.error("调用用户服务获取购物车异常, userId: {}", userId, e);
            throw new ServiceException("获取用户购物车信息失败: " + e.getMessage());
        }
    }

    /**
     * 验证请求的商品是否在购物车中，并获取匹配的商品信息
     */
    private List<OrderItemData> validateAndGetOrderItems(CreateOrderRequest request, CartResponse cartResponse) {
        List<OrderItemData> orderItemDataList = new ArrayList<>();

        // 将购物车商品按SKU分组
        Map<String, CartItemResponse> cartItemMap = cartResponse.getItems().stream()
                .collect(Collectors.toMap(CartItemResponse::getSku, item -> item));

        for (CreateOrderRequest.OrderItemRequest requestItem : request.getItems()) {
            CartItemResponse cartItem = cartItemMap.get(requestItem.getSku());
            if (cartItem == null) {
                throw new ServiceException("商品 " + requestItem.getSku() + " 不在购物车中");
            }

            // 验证尺码和数量
            for (CreateOrderRequest.SizeDetailRequest sizeDetailRequest : requestItem.getSizeDetails()) {
                SizeDetailSumResponse matchingSizeDetail = cartItem.getSizeDetailSums().stream()
                        .filter(detail -> detail.getSize().equals(sizeDetailRequest.getSize()))
                        .findFirst()
                        .orElse(null);

                if (matchingSizeDetail == null) {
                    throw new ServiceException("商品 " + requestItem.getSku() + " 尺码 " + sizeDetailRequest.getSize() + " 不在购物车中");
                }

                if (matchingSizeDetail.getQuantity() < sizeDetailRequest.getQuantity()) {
                    throw new ServiceException("商品 " + requestItem.getSku() + " 尺码 " + sizeDetailRequest.getSize() +
                            " 购物车数量不足，购物车数量: " + matchingSizeDetail.getQuantity() +
                            ", 请求数量: " + sizeDetailRequest.getQuantity());
                }

                // 创建订单商品数据
                OrderItemData orderItemData = new OrderItemData();
                orderItemData.setSku(requestItem.getSku());
                orderItemData.setProductName(cartItem.getProductName());
                orderItemData.setSize(sizeDetailRequest.getSize());
                orderItemData.setQuantity(sizeDetailRequest.getQuantity());
                orderItemData.setUnitPrice(new BigDecimal(matchingSizeDetail.getAvgPrice()));
                orderItemDataList.add(orderItemData);
            }
        }

        return orderItemDataList;
    }

    /**
     * 计算订单总金额
     */
    private BigDecimal calculateTotalAmount(List<OrderItemData> orderItemDataList) {
        return orderItemDataList.stream()
                .map(item -> item.getUnitPrice().multiply(new BigDecimal(item.getQuantity())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 创建订单主表记录
     */
    private SysOrderGroup createOrderGroup(String orderId, Long userId, BigDecimal totalAmount) {
        SysOrderGroup orderGroup = new SysOrderGroup();
        orderGroup.setOrderId(orderId);
        orderGroup.setUserId(userId);
        orderGroup.setTotalAmount(totalAmount);
        orderGroup.setStatus(KnetOrderStatus.PENDING_PAYMENT);
        return orderGroup;
    }

    /**
     * 创建订单明细记录
     */
    private List<SysOrderItem> createOrderItems(String orderId, List<OrderItemData> orderItemDataList) {
        List<SysOrderItem> orderItems = new ArrayList<>();

        for (OrderItemData itemData : orderItemDataList) {
            SysOrderItem orderItem = new SysOrderItem();
            orderItem.setOrderId(orderId);
            orderItem.setSku(itemData.getSku());
            orderItem.setSize(itemData.getSize());
            orderItem.setName(itemData.getProductName());
            orderItem.setPrice(itemData.getUnitPrice());
            orderItem.setCount(itemData.getQuantity());
            orderItem.setStatus(KnetOrderItemStatus.PENDING_PAYMENT);
            orderItems.add(orderItem);
        }

        return orderItems;
    }

    /**
     * 构建创建订单响应
     */
    private CreateOrderResponse buildCreateOrderResponse(SysOrderGroup orderGroup, List<SysOrderItem> orderItems) {
        List<CreateOrderResponse.OrderItemResponse> itemResponses = orderItems.stream()
                .map(item -> CreateOrderResponse.OrderItemResponse.builder()
                        .itemId(item.getItemId())
                        .sku(item.getSku())
                        .name(item.getName())
                        .size(item.getSize())
                        .price(PriceFormatUtil.formatPrice(item.getPrice()))
                        .count(item.getCount())
                        .subtotal(PriceFormatUtil.formatPrice(item.getPrice().multiply(new BigDecimal(item.getCount()))))
                        .build())
                .collect(Collectors.toList());

        return CreateOrderResponse.builder()
                .orderId(orderGroup.getOrderId())
                .userId(orderGroup.getUserId())
                .totalAmount(PriceFormatUtil.formatPrice(orderGroup.getTotalAmount()))
                .status(orderGroup.getStatus())
                .items(itemResponses)
                .build();
    }

    /**
     * 订单商品数据内部类
     */
    private static class OrderItemData {
        private String sku;
        private String productName;
        private String size;
        private Integer quantity;
        private BigDecimal unitPrice;

        // Getters and Setters
        public String getSku() { return sku; }
        public void setSku(String sku) { this.sku = sku; }
        public String getProductName() { return productName; }
        public void setProductName(String productName) { this.productName = productName; }
        public String getSize() { return size; }
        public void setSize(String size) { this.size = size; }
        public Integer getQuantity() { return quantity; }
        public void setQuantity(Integer quantity) { this.quantity = quantity; }
        public BigDecimal getUnitPrice() { return unitPrice; }
        public void setUnitPrice(BigDecimal unitPrice) { this.unitPrice = unitPrice; }
    }
}
