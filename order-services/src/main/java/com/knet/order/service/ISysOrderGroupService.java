package com.knet.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.knet.order.model.dto.req.CreateOrderRequest;
import com.knet.order.model.dto.rsp.CreateOrderResponse;
import com.knet.order.model.entity.SysOrderGroup;

/**
 * <AUTHOR>
 * @date 2025-03-11 15:45:02
 * @description: 针对表【sys_order(B2B订单主表)】的数据库操作Service
 */
public interface ISysOrderGroupService extends IService<SysOrderGroup> {

    /**
     * 创建订单 - 保存订单信息
     * 发送订单创建消息
     *
     * @param sysOrderGroup 订单实体
     */
    void createOrder(SysOrderGroup sysOrderGroup);

    /**
     * 从购物车创建订单
     *
     * @param request 创建订单请求
     * @return 创建订单响应
     */
    CreateOrderResponse createOrderFromCart(CreateOrderRequest request);
}
