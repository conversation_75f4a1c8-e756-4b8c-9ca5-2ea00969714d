package com.knet.order.system.listener;

import com.alibaba.fastjson2.JSON;
import com.knet.order.model.entity.SysOrderGroup;
import com.knet.order.mq.producer.OrderProducer;
import com.knet.order.system.event.OrderCreatedEvent;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/3/21 17:24
 * @description: OrderCreatedEventMessageListener 订单创建事件消息监听器
 */
@Component
public class OrderCreatedEventMessageListener {
    @Resource
    private OrderProducer orderProducer;

    @TransactionalEventListener(
            classes = OrderCreatedEvent.class,
            // 事务提交后执行
            phase = TransactionPhase.AFTER_COMMIT
    )
    public void handleOrderCreatedEvent(OrderCreatedEvent event) {
        SysOrderGroup order = event.getOrder();
        orderProducer.sendOrderCreateEvent(JSON.toJSONString(order));
    }
}
