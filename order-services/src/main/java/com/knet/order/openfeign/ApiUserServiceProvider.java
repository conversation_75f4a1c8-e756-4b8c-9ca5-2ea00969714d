package com.knet.order.openfeign;

import com.knet.common.base.HttpResult;
import com.knet.order.model.dto.user.CartResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * @date 2025/6/3 16:40
 * @description: 用户服务API客户端
 */
@FeignClient(name = "user-services", path = "/userService")
public interface ApiUserServiceProvider {

    /**
     * 获取用户购物车信息
     *
     * @param userId 用户ID
     * @return 购物车信息
     */
    @GetMapping("/cart/{userId}")
    HttpResult<CartResponse> getCart(@PathVariable("userId") Long userId);
}
