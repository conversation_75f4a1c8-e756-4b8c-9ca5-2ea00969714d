package com.knet.user.controller;

import com.knet.common.annotation.Loggable;
import com.knet.common.annotation.ModifyHeader;
import com.knet.common.base.HttpResult;
import com.knet.common.constants.SystemConstant;
import com.knet.common.exception.ServiceException;
import com.knet.user.model.dto.req.AddToCartRequest;
import com.knet.user.model.dto.req.EditCartItemRequest;
import com.knet.user.model.dto.req.RemoveFromCartRequest;
import com.knet.user.model.dto.req.UpdateCartRequest;
import com.knet.user.model.dto.rsp.CartResponse;
import com.knet.user.model.dto.rsp.EditSizeDetailResponse;
import com.knet.user.service.IShopCartService;
import com.knet.common.context.UserContext;
import com.knet.user.system.utils.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/5/22 14:00
 * @description: 用户购物车接口
 */
@Slf4j
@RestController
@RequestMapping("/shop-cart")
@Tag(name = "用户购物车控制器", description = "用户购物车控制器")
public class ShopCartController {
    @Resource
    private JwtUtil jwtUtil;
    @Resource
    private IShopCartService shopCartService;

    /**
     * 添加商品到购物车
     *
     * @param request 添加商品请求
     * @return 购物车信息
     */
    @Loggable(value = "添加商品到购物车")
    @ModifyHeader(value = SystemConstant.TOKEN, handlerType = "USER_TOKEN")
    @Operation(summary = "添加商品到购物车", description = "添加商品到购物车，返回更新后的购物车信息")
    @PostMapping("/add")
    public HttpResult<CartResponse> addToCart(@Validated @RequestBody AddToCartRequest request) {
        log.info("添加商品到购物车请求: {}", request);
        Long userIdFromToken = Long.valueOf(jwtUtil.getUserIdFromToken(UserContext.getContext()));
        if (!userIdFromToken.equals(request.getUserId())) {
            throw new ServiceException("不能操作其他人的购物车");
        }
        CartResponse response = shopCartService.addToCart(request);
        return HttpResult.ok(response);
    }

    /**
     * 更新购物车商品
     *
     * @param request 更新购物车请求
     * @return 购物车信息
     */
    @Loggable(value = "更新购物车商品")
    @ModifyHeader(value = SystemConstant.TOKEN, handlerType = "USER_TOKEN")
    @Operation(summary = "更新购物车商品", description = "更新购物车商品数量或选中状态")
    @PutMapping("/update")
    public HttpResult<Void> updateCart(@Validated @RequestBody UpdateCartRequest request) {
        log.info("更新购物车商品请求: {}", request);
        Long userIdFromToken = Long.valueOf(jwtUtil.getUserIdFromToken(UserContext.getContext()));
        if (!userIdFromToken.equals(request.getUserId())) {
            throw new ServiceException("不能操作其他人的购物车");
        }
        shopCartService.updateCart(request);
        return HttpResult.ok();
    }

    /**
     * 从购物车移除商品
     *
     * @param request 移除商品请求
     * @return 购物车信息
     */
    @Loggable(value = "从购物车移除商品")
    @ModifyHeader(value = SystemConstant.TOKEN, handlerType = "USER_TOKEN")
    @Operation(summary = "从购物车移除商品", description = "从购物车移除商品")
    @PostMapping("/remove")
    public HttpResult<Void> removeFromCart(@Validated @RequestBody RemoveFromCartRequest request) {
        log.info("从购物车移除商品请求: {}", request);
        Long userIdFromToken = Long.valueOf(jwtUtil.getUserIdFromToken(UserContext.getContext()));
        if (!userIdFromToken.equals(request.getUserId())) {
            throw new ServiceException("不能操作其他人的购物车");
        }
        shopCartService.removeFromCart(request);
        return HttpResult.ok();
    }

    /**
     * 获取购物车信息
     *
     * @param userId 用户ID
     * @return 购物车信息
     */
    @Loggable(value = "获取购物车信息")
    @ModifyHeader(value = SystemConstant.TOKEN, handlerType = "USER_TOKEN")
    @Operation(summary = "获取购物车信息", description = "获取用户购物车信息")
    @GetMapping("/{userId}")
    public HttpResult<CartResponse> getCart(@PathVariable Long userId) {
        log.info("获取购物车信息请求, userId: {}", userId);
        Long userIdFromToken = Long.valueOf(jwtUtil.getUserIdFromToken(UserContext.getContext()));
        if (!userIdFromToken.equals(userId)) {
            throw new ServiceException("不能操作其他人的购物车");
        }
        CartResponse response = shopCartService.getCart(userId);
        return HttpResult.ok(response);
    }

    /**
     * 获取购物车中的特定商品-适用于购物车编辑
     *
     * @return 获取购物车中的特定商品信息
     */
    @Loggable(value = "获取购物车中的特定商品-适用于购物车编辑")
    @ModifyHeader(value = SystemConstant.TOKEN, handlerType = "USER_TOKEN")
    @Operation(summary = "获取购物车中的特定商品-适用于购物车编辑", description = "根据用户ID、商品SKU和尺码获取购物车中的特定商品-适用于购物车编辑")
    @PostMapping("/cart-item")
    public HttpResult<EditSizeDetailResponse> getCartItem(@Validated @RequestBody EditCartItemRequest request) {
        log.info("获取购物车中的特定商品-适用于购物车编辑 请求: {}", request);
        Long userIdFromToken = Long.valueOf(jwtUtil.getUserIdFromToken(UserContext.getContext()));
        if (!userIdFromToken.equals(request.getUserId())) {
            throw new ServiceException("不能操作其他人的购物车");
        }
        EditSizeDetailResponse response = shopCartService.getCartItem(request);
        return HttpResult.ok(response);
    }
}
