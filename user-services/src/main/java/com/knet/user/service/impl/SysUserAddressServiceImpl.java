package com.knet.user.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.knet.common.annotation.DistributedLock;
import com.knet.common.context.UserContext;
import com.knet.common.exception.ServiceException;
import com.knet.user.mapper.SysUserAddressMapper;
import com.knet.user.model.dto.req.UserAddressQueryRequest;
import com.knet.user.model.dto.req.UserAddressSaveRequest;
import com.knet.user.model.dto.rsp.UserAddressDtoResp;
import com.knet.user.model.entity.SysUserAddress;
import com.knet.user.service.ISysUserAddressService;
import com.knet.user.system.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 针对表【sys_user_address(用户地址表)】的数据库操作Service实现
 * @date 2025-05-07 14:25:18
 */
@Slf4j
@Service
public class SysUserAddressServiceImpl extends ServiceImpl<SysUserAddressMapper, SysUserAddress>
        implements ISysUserAddressService {
    @Resource
    private JwtUtil jwtUtil;

    @DistributedLock(key = "'createUserAddress:'+#request.hashCode()", expire = 1)
    @Override
    public SysUserAddress createAddress(UserAddressSaveRequest request) {
        String userId = jwtUtil.getUserIdFromToken(UserContext.getContext());
        if (StrUtil.isBlank(userId)) {
            throw new ServiceException("用户未登录");
        }
        request.setUserId(Long.valueOf(userId));
        SysUserAddress address = SysUserAddress.createAddress(request);
        try {
            this.save(address);
        } catch (Exception e) {
            throw new ServiceException("create user address failed");
        }
        return this.getById(address.getId());
    }

    @Override
    public IPage<UserAddressDtoResp> listAddress(UserAddressQueryRequest request) {
        String userId = jwtUtil.getUserIdFromToken(UserContext.getContext());
        if (StrUtil.isBlank(userId)) {
            throw new ServiceException("用户未登录");
        }
        LambdaQueryWrapper<SysUserAddress> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(SysUserAddress::getUserId, Long.valueOf(userId))
                .orderByDesc(SysUserAddress::getCreateTime);
        Page<SysUserAddress> page = new Page<>(request.getPageNo(), request.getPageSize());
        IPage<SysUserAddress> userAddressPage = baseMapper.selectPage(page, queryWrapper);
        return userAddressPage.convert(SysUserAddress::mapToUserInfoDtoResp);
    }

    @DistributedLock(key = "'deleteUserAddress:'+#id", expire = 1)
    @Override
    public void deleteUserAddress(Long id) {
        if (null == id || id <= 0) {
            throw new ServiceException("用户地址ID不能为空,或者用户地址ID不合法");
        }
        try {
            String userId = jwtUtil.getUserIdFromToken(UserContext.getContext());
            lambdaUpdate()
                    .eq(SysUserAddress::getId, id)
                    .eq(SysUserAddress::getUserId, userId)
                    .remove();
        } catch (Exception e) {
            log.error("删除用户地址失败,用户地址已被删除，或者用户地址不存在,id {}", id);
            throw new ServiceException("删除用户失败");
        }
    }

    @DistributedLock(key = "'updateUserAddress:'+#id", expire = 1)
    @Override
    public SysUserAddress updateUserAddress(Long id, UserAddressSaveRequest request) {
        if (null == id || id <= 0) {
            throw new ServiceException("用户地址ID不能为空,或者用户地址ID不合法");
        }
        try {
            String userId = jwtUtil.getUserIdFromToken(UserContext.getContext());
            if (StrUtil.isBlank(userId)) {
                throw new ServiceException("用户未登录");
            }
            SysUserAddress existingAddress = lambdaQuery()
                    .eq(SysUserAddress::getId, id)
                    .eq(SysUserAddress::getUserId, userId)
                    .one();
            if (existingAddress == null) {
                throw new ServiceException("用户地址不存在或无权限修改");
            }
            SysUserAddress updatedAddress = SysUserAddress.createAddress(request);
            updatedAddress.setId(id);
            updatedAddress.setUserId(Long.valueOf(userId));
            boolean updated = this.updateById(updatedAddress);
            if (!updated) {
                throw new ServiceException("更新用户地址失败");
            }
            return this.getById(id);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新用户地址失败, id: {}, 错误: {}", id, e.getMessage(), e);
            throw new ServiceException("更新用户地址失败");
        }
    }
}




